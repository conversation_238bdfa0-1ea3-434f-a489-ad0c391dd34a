#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据文件合并系统
按平台自动合并客服绩效数据文件，处理不同标题格式的Excel文件合并
"""

import re
import pandas as pd
from pathlib import Path
from collections import defaultdict
import logging
from datetime import datetime
import warnings
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import tkinter as tk
from tkinter import filedialog, messagebox

# 忽略pandas警告
warnings.filterwarnings('ignore')

class DataMerger:
    def __init__(self, root_path=None):
        """
        初始化数据合并器

        Args:
            root_path (str): 根目录路径，如果为None则弹出选择对话框
        """
        if root_path is None:
            root_path = self.select_data_directory()
            if not root_path:
                raise ValueError("未选择数据目录")

        self.root_path = Path(root_path)
        self.platforms = ['DY', 'TM', 'JD', 'PDD']
        self.platform_names = {
            'DY': '抖音',
            'TM': '天猫',
            'JD': '京东',
            'PDD': '拼多多'
        }

        # 设置日志
        self.setup_logging()

        # 存储文件信息
        self.file_info = defaultdict(list)

    def select_data_directory(self):
        """
        弹出对话框选择数据目录

        Returns:
            str: 选择的目录路径，如果取消则返回None
        """
        # 创建隐藏的根窗口
        root = tk.Tk()
        root.withdraw()

        # 弹出目录选择对话框
        directory = filedialog.askdirectory(
            title="请选择数据目录（如：（客服部）新客服绩效及数据2025-05-01_2025-05-31）",
            initialdir="."
        )

        root.destroy()

        if directory:
            # 验证选择的目录是否包含平台子目录
            selected_path = Path(directory)
            platform_dirs = []
            for platform in ['DY', 'TM', 'JD', 'PDD']:
                platform_path = selected_path / platform
                if platform_path.exists():
                    platform_dirs.append(platform)

            if platform_dirs:
                print(f"已选择数据目录: {directory}")
                print(f"发现平台目录: {', '.join(platform_dirs)}")
                return directory
            else:
                # 弹出警告对话框
                root = tk.Tk()
                root.withdraw()
                messagebox.showwarning(
                    "目录验证失败",
                    f"选择的目录中未找到平台子目录（DY、TM、JD、PDD）\n请选择正确的数据目录"
                )
                root.destroy()
                return None

        return None

    def setup_logging(self):
        """设置日志记录"""
        log_filename = f"data_merger_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def parse_filename(self, filename):
        """
        解析文件名，提取店铺、类型、时间信息
        
        Args:
            filename (str): 文件名
            
        Returns:
            dict: 包含店铺、类型、时间信息的字典
        """
        # 移除文件扩展名
        name_without_ext = filename.replace('.xlsx', '').replace('.xls', '')
        
        # 使用正则表达式解析文件名
        # 匹配模式：店铺名_类型部分_时间部分
        # 时间部分通常是日期格式或包含数字的格式
        
        # 先尝试匹配标准的日期格式 YYYY-MM-DD 或 YYYY_MM_DD
        date_pattern = r'(\d{4}[-_]\d{2}[-_]\d{2})'
        date_match = re.search(date_pattern, name_without_ext)
        
        if date_match:
            date_part = date_match.group(1)
            # 分割文件名，最后一个日期前的部分作为类型
            parts_before_date = name_without_ext[:date_match.start()].rstrip('_-')
            parts = parts_before_date.split('_')
            
            if len(parts) >= 2:
                shop_name = parts[0]
                data_type = '_'.join(parts[1:])
            else:
                shop_name = parts[0] if parts else "未知店铺"
                data_type = "未知类型"
                
            return {
                'shop': shop_name,
                'type': data_type,
                'date': date_part,
                'original_filename': filename
            }
        
        # 如果没有标准日期格式，尝试其他时间格式
        # 匹配包含时间戳的格式
        timestamp_pattern = r'(\d{4}_\d{2}_\d{2}_\d{2}_\d{2}_\d{2})'
        timestamp_match = re.search(timestamp_pattern, name_without_ext)
        
        if timestamp_match:
            timestamp_part = timestamp_match.group(1)
            parts_before_timestamp = name_without_ext[:timestamp_match.start()].rstrip('_-')
            parts = parts_before_timestamp.split('_')
            
            if len(parts) >= 2:
                shop_name = parts[0]
                data_type = '_'.join(parts[1:])
            else:
                shop_name = parts[0] if parts else "未知店铺"
                data_type = "未知类型"
                
            return {
                'shop': shop_name,
                'type': data_type,
                'date': timestamp_part,
                'original_filename': filename
            }
        
        # 如果都没有匹配到，使用最后一个下划线分割
        parts = name_without_ext.split('_')
        if len(parts) >= 3:
            shop_name = parts[0]
            data_type = '_'.join(parts[1:-1])
            date_part = parts[-1]
        elif len(parts) == 2:
            shop_name = parts[0]
            data_type = parts[1]
            date_part = "未知时间"
        else:
            shop_name = name_without_ext
            data_type = "未知类型"
            date_part = "未知时间"
            
        return {
            'shop': shop_name,
            'type': data_type,
            'date': date_part,
            'original_filename': filename
        }
    
    def scan_files(self):
        """扫描所有Excel文件并分类"""
        self.logger.info("开始扫描文件...")
        self.logger.info(f"数据目录: {self.root_path}")

        for platform in self.platforms:
            platform_path = self.root_path / platform
            
            if not platform_path.exists():
                self.logger.warning(f"平台目录不存在: {platform_path}")
                continue
                
            self.logger.info(f"扫描平台: {self.platform_names[platform]} ({platform})")
            
            # 递归查找所有Excel文件
            for excel_file in platform_path.rglob("*.xlsx"):
                if excel_file.is_file():
                    file_info = self.parse_filename(excel_file.name)
                    file_info['platform'] = platform
                    file_info['full_path'] = excel_file
                    
                    # 按平台_类型分组
                    group_key = f"{platform}_{file_info['type']}"
                    self.file_info[group_key].append(file_info)
                    
                    # 减少文件发现日志输出
        
        self.logger.info(f"扫描完成，共发现 {sum(len(files) for files in self.file_info.values())} 个文件")
        self.logger.info(f"分为 {len(self.file_info)} 个合并组")
        
    def clean_and_convert_data(self, df):
        """
        清理数据：去除前后空格，转换数字类型（改进版本）

        Args:
            df (pd.DataFrame): 数据框

        Returns:
            pd.DataFrame: 清理后的数据框
        """
        if df.empty:
            return df

        df_cleaned = df.copy()

        # 第一步：清理所有字符串列的前后空格和特殊字符
        for col in df_cleaned.columns:
            if df_cleaned[col].dtype == 'object':
                # 去除前后空格
                df_cleaned[col] = df_cleaned[col].astype(str).str.strip()
                # 将 'nan' 字符串转回 None（而不是pd.NA，避免Excel写入问题）
                df_cleaned[col] = df_cleaned[col].replace('nan', None)
                # 去除其他可能的空白字符
                df_cleaned[col] = df_cleaned[col].str.replace('\xa0', ' ', regex=False)  # 不间断空格
                df_cleaned[col] = df_cleaned[col].str.replace('\u3000', ' ', regex=False)  # 全角空格
                df_cleaned[col] = df_cleaned[col].str.strip()

        # 第二步：尝试将看起来像数字的列转换为数字
        for col in df_cleaned.columns:
            if df_cleaned[col].dtype == 'object':
                original_series = df_cleaned[col].copy()
                converted_series = self.try_convert_to_numeric(df_cleaned[col])

                # 记录重要的转换情况（减少日志输出）
                if not converted_series.equals(original_series):
                    if converted_series.dtype in ['int64', 'float64']:
                        converted_count = converted_series.notna().sum()
                        total_count = len(converted_series)
                        # 只记录转换率较高的列
                        if converted_count / total_count > 0.8:
                            self.logger.info(f"列 '{col}' 转换为数字类型: {converted_series.dtype}")

                df_cleaned[col] = converted_series

        return df_cleaned

    def try_convert_to_numeric(self, series):
        """
        尝试将系列转换为数字类型（完全转换版本）

        Args:
            series (pd.Series): 数据系列

        Returns:
            pd.Series: 转换后的系列
        """
        if series.dtype == 'object':
            # 创建副本避免修改原数据
            series_str = series.astype(str)

            # 检查是否包含百分号
            has_percentage = series_str.str.contains('%', na=False).any()

            # 清理数据：移除空格、逗号、括号等
            series_clean = series_str.str.strip()
            series_clean = series_clean.str.replace(',', '', regex=False)  # 移除千位分隔符
            series_clean = series_clean.str.replace('，', '', regex=False)  # 移除中文逗号
            series_clean = series_clean.str.replace('(', '', regex=False)  # 移除左括号
            series_clean = series_clean.str.replace(')', '', regex=False)  # 移除右括号
            series_clean = series_clean.str.replace('（', '', regex=False)  # 移除中文左括号
            series_clean = series_clean.str.replace('）', '', regex=False)  # 移除中文右括号

            if has_percentage:
                # 处理百分比：转换为Excel百分比数据类型（小数形式）
                numeric_part = series_clean.str.replace('%', '', regex=False)
                numeric_part = numeric_part.replace('nan', None)

                try:
                    # 转换数字部分
                    numeric_values = pd.to_numeric(numeric_part, errors='coerce')

                    # 如果有任何成功转换的值，转换为小数形式（Excel百分比格式）
                    if numeric_values.notna().any():
                        # 转换为小数形式，Excel会自动识别为百分比
                        # 例如：50% -> 0.5，100% -> 1.0
                        return numeric_values / 100
                    else:
                        return series
                except:
                    return series
            else:
                # 处理普通数字
                series_clean = series_clean.replace('nan', None)

                try:
                    # 尝试转换为数字
                    numeric_series = pd.to_numeric(series_clean, errors='coerce')

                    # 如果有任何成功转换的值，就使用数字类型
                    if numeric_series.notna().any():
                        return numeric_series
                    else:
                        return series
                except:
                    return series

        return series

    def read_excel_file(self, file_path):
        """
        读取Excel文件（优化版本）

        Args:
            file_path (Path): 文件路径

        Returns:
            pd.DataFrame: 数据框
        """
        try:
            # 优化：直接读取，减少重复读取
            df = pd.read_excel(file_path, engine='openpyxl')

            # 检查是否为空或第一行全为空
            if df.empty:
                return pd.DataFrame()

            # 如果第一行全为空，尝试从第二行开始读取
            if df.iloc[0].isna().all():
                df = pd.read_excel(file_path, engine='openpyxl', header=1)
                if df.empty:
                    return pd.DataFrame()

            # 优化：批量清理列名
            df.columns = [
                str(col).strip() if pd.notna(col) else f"未命名列_{i}"
                for i, col in enumerate(df.columns)
            ]

            # 清理数据：去除空格，转换数字
            df = self.clean_and_convert_data(df)

            return df

        except Exception as e:
            self.logger.error(f"读取文件失败 {file_path}: {str(e)}")
            return pd.DataFrame()
    
    def merge_dataframes(self, dataframes, group_key):
        """
        合并多个数据框，处理标题不一致的情况（优化版本）

        Args:
            dataframes (list): 数据框列表
            group_key (str): 分组键

        Returns:
            pd.DataFrame: 合并后的数据框
        """
        if not dataframes:
            return pd.DataFrame()

        if len(dataframes) == 1:
            return dataframes[0].copy()

        self.logger.info(f"合并 {group_key} 组的 {len(dataframes)} 个文件")

        # 优化：使用集合快速收集所有列名
        base_columns = list(dataframes[0].columns)
        all_columns_set = set(base_columns)

        # 收集所有其他数据框中的新列
        for df in dataframes[1:]:
            all_columns_set.update(df.columns)

        # 保持基准列顺序，新列追加到末尾
        additional_columns = [col for col in all_columns_set if col not in base_columns]
        final_columns = base_columns + additional_columns

        # 优化：使用 reindex 方法统一列结构，比逐个添加列更高效
        unified_dfs = []
        for df in dataframes:
            # 使用 reindex 重新排列列，缺失列自动填充 NaN
            df_reindexed = df.reindex(columns=final_columns)
            unified_dfs.append(df_reindexed)

        # 合并所有数据框
        merged_df = pd.concat(unified_dfs, ignore_index=True)

        # 处理"新平均响应时间"字段
        merged_df = self.handle_response_time_columns(merged_df)

        self.logger.info(f"合并完成，共 {len(merged_df)} 行数据，{len(merged_df.columns)} 列")

        return merged_df

    def handle_response_time_columns(self, df):
        """
        处理响应时间列，将"新平均响应时间"合并到"平均响应时间"

        Args:
            df (pd.DataFrame): 数据框

        Returns:
            pd.DataFrame: 处理后的数据框
        """
        if df.empty:
            return df

        df_result = df.copy()

        # 检查是否同时存在"平均响应时间"和"新平均响应时间"
        if '平均响应时间' in df_result.columns and '新平均响应时间' in df_result.columns:
            # 用"新平均响应时间"的非空值更新"平均响应时间"
            mask = df_result['新平均响应时间'].notna()
            df_result.loc[mask, '平均响应时间'] = df_result.loc[mask, '新平均响应时间']

            # 删除"新平均响应时间"列
            df_result = df_result.drop(columns=['新平均响应时间'])

        elif '新平均响应时间' in df_result.columns and '平均响应时间' not in df_result.columns:
            # 如果只有"新平均响应时间"，重命名为"平均响应时间"
            df_result = df_result.rename(columns={'新平均响应时间': '平均响应时间'})

        return df_result

    def extract_year_month_from_root_dir(self):
        """
        从根目录名称中提取年月格式

        Returns:
            str: 年月格式字符串 (YYYYMM)
        """
        # 从根目录路径中提取时间信息
        for part in self.root_path.parts:
            # 匹配包含日期范围的目录名，如：（客服部）新客服绩效及数据2025-05-01_2025-05-31
            date_match = re.search(r'(\d{4})-(\d{2})-\d{2}_\d{4}-\d{2}-\d{2}', part)
            if date_match:
                year, month = date_match.groups()
                return f"{year}{month}"

        # 如果没有找到，检查当前工作目录
        current_dir = Path.cwd()
        for part in current_dir.parts:
            date_match = re.search(r'(\d{4})-(\d{2})-\d{2}_\d{4}-\d{2}-\d{2}', part)
            if date_match:
                year, month = date_match.groups()
                return f"{year}{month}"

        # 默认值
        return "202505"

    def add_time_column(self, df, file_info_list=None):
        """
        在数据框第一列添加时间列（年月格式）

        Args:
            df (pd.DataFrame): 数据框
            file_info_list (list): 文件信息列表（保持接口兼容，未使用）

        Returns:
            pd.DataFrame: 添加时间列后的数据框
        """
        # 忽略未使用的参数警告
        _ = file_info_list
        if df.empty:
            return df

        # 统一使用根目录时间，确保为数字格式
        time_value = self.extract_year_month_from_root_dir()
        # 确保时间值是数字类型
        try:
            time_value = int(time_value)
        except:
            time_value = 202505  # 默认数字值

        # 创建新的数据框，时间列在第一列
        df_with_time = df.copy()
        df_with_time.insert(0, '时间', time_value)

        return df_with_time

    def convert_response_time_to_seconds(self, time_str):
        """
        将拼多多的响应时长转换为秒
        格式：--分--秒，非数字即为0分0秒

        Args:
            time_str (str): 时间字符串

        Returns:
            int: 秒数
        """
        if pd.isna(time_str) or not isinstance(time_str, str):
            return 0

        try:
            # 匹配 数字分数字秒 的格式
            match = re.search(r'(\d+)分(\d+)秒', str(time_str))
            if match:
                minutes = int(match.group(1))
                seconds = int(match.group(2))
                return minutes * 60 + seconds
            else:
                return 0
        except:
            return 0

    def clean_platform_data(self, df, platform):
        """
        根据平台清理数据，去掉汇总行（改进版本，处理空格和部分匹配）

        Args:
            df (pd.DataFrame): 数据框
            platform (str): 平台代码

        Returns:
            pd.DataFrame: 清理后的数据框
        """
        if df.empty:
            return df

        df_cleaned = df.copy()

        if platform == 'PDD':
            # 拼多多：去掉客服账号包含"店铺总计"或"主账号"的行
            if '客服账号' in df_cleaned.columns:
                # 先清理空格，然后检查是否包含关键词
                mask = df_cleaned['客服账号'].astype(str).str.strip().str.contains(
                    '店铺总计|主账号', case=False, na=False
                )
                df_cleaned = df_cleaned[~mask]
                self.logger.info(f"拼多多去掉了 {mask.sum()} 行汇总数据")

            # 拼多多：添加响应时长转换列
            if '平均人工响应时长' in df_cleaned.columns:
                df_cleaned['平均人工响应时长2'] = df_cleaned['平均人工响应时长'].apply(
                    self.convert_response_time_to_seconds
                )
                # 将新列插入到原列后面
                cols = list(df_cleaned.columns)
                original_idx = cols.index('平均人工响应时长')
                new_col = cols.pop()  # 移除最后一列（新添加的列）
                cols.insert(original_idx + 1, new_col)  # 插入到原列后面
                df_cleaned = df_cleaned[cols]

        elif platform == 'JD':
            # 京东：去掉客服包含"总值"和"均值"的行
            for col in df_cleaned.columns:
                if '客服' in col:
                    mask = df_cleaned[col].astype(str).str.strip().str.contains(
                        '总值|均值', case=False, na=False
                    )
                    removed_count = mask.sum()
                    df_cleaned = df_cleaned[~mask]
                    if removed_count > 0:
                        self.logger.info(f"京东去掉了 {removed_count} 行汇总数据")
                    break

        elif platform == 'DY':
            # 抖音：去掉客服昵称包含"汇总"和"均值"的行
            if '客服昵称' in df_cleaned.columns:
                mask = df_cleaned['客服昵称'].astype(str).str.strip().str.contains(
                    '汇总|均值', case=False, na=False
                )
                removed_count = mask.sum()
                df_cleaned = df_cleaned[~mask]
                if removed_count > 0:
                    self.logger.info(f"抖音去掉了 {removed_count} 行汇总数据")

        elif platform == 'TM':
            # 天猫：去掉旺旺包含"汇总"和"均值"的行
            if '旺旺' in df_cleaned.columns:
                mask = df_cleaned['旺旺'].astype(str).str.strip().str.contains(
                    '汇总|均值', case=False, na=False
                )
                removed_count = mask.sum()
                df_cleaned = df_cleaned[~mask]
                if removed_count > 0:
                    self.logger.info(f"天猫去掉了 {removed_count} 行汇总数据")

        return df_cleaned

    def prepare_for_excel(self, df):
        """
        准备数据框以便保存到Excel，处理NA值问题

        Args:
            df (pd.DataFrame): 数据框

        Returns:
            pd.DataFrame: 处理后的数据框
        """
        df_excel = df.copy()

        # 将所有pd.NA替换为None，避免Excel写入错误
        df_excel = df_excel.where(pd.notna(df_excel), None)

        return df_excel

    def save_excel_with_formatting(self, df, output_path):
        """
        保存Excel文件并设置百分比格式

        Args:
            df (pd.DataFrame): 数据框
            output_path (Path): 输出路径
        """
        from openpyxl import Workbook
        from openpyxl.utils.dataframe import dataframe_to_rows
        from openpyxl.styles import NamedStyle

        # 准备数据以便Excel保存
        df_excel = self.prepare_for_excel(df)

        # 创建工作簿
        wb = Workbook()
        ws = wb.active

        # 写入数据
        for r in dataframe_to_rows(df_excel, index=False, header=True):
            ws.append(r)

        # 创建百分比样式
        percent_style = NamedStyle(name="percent")
        percent_style.number_format = '0.00%'

        # 识别百分比列并应用格式
        for col_idx, col_name in enumerate(df.columns, 1):
            # 检查列是否包含0-1之间的小数值（可能是百分比）
            if df[col_name].dtype in ['float64', 'int64']:
                # 检查是否所有非空值都在0-1之间（百分比特征）
                non_null_values = df[col_name].dropna()
                if len(non_null_values) > 0:
                    # 如果大部分值在0-1之间，且列名包含率、比等关键词，认为是百分比
                    in_percent_range = ((non_null_values >= 0) & (non_null_values <= 1)).sum()
                    total_values = len(non_null_values)

                    is_percentage_column = (
                        in_percent_range / total_values > 0.7 and  # 70%的值在0-1之间
                        ('率' in col_name or '比' in col_name or 'rate' in col_name.lower())
                    )

                    if is_percentage_column:
                        # 应用百分比格式到整列（除了标题行）
                        for row_idx in range(2, len(df) + 2):  # 从第2行开始（跳过标题）
                            cell = ws.cell(row=row_idx, column=col_idx)
                            cell.number_format = '0.00%'

        # 保存文件
        wb.save(output_path)

    def merge_jingdong_data(self, merged_df, group_key):
        """
        处理京东数据的特殊合并需求

        Args:
            merged_df (pd.DataFrame): 已合并的数据框
            group_key (str): 分组键

        Returns:
            pd.DataFrame: 处理后的数据框
        """
        # 只处理京东工作量对比数据
        if group_key != 'JD_客服数据对比_工作量对比':
            return merged_df

        # 查找京东售前销售绩效对比数据
        sales_group_key = 'JD_客服数据对比_售前销售绩效对比'
        if sales_group_key not in self.file_info:
            self.logger.warning(f"未找到 {sales_group_key} 数据，无法补充售前接待人数和促成出库人数")
            return merged_df

        try:
            # 读取售前销售绩效对比数据
            sales_dataframes = []
            for file_info in self.file_info[sales_group_key]:
                df = self.read_excel_file(file_info['full_path'])
                if not df.empty:
                    # 清理数据
                    df = self.clean_platform_data(df, 'JD')
                    sales_dataframes.append(df)

            if not sales_dataframes:
                self.logger.warning("售前销售绩效对比数据为空")
                return merged_df

            # 合并售前数据
            sales_df = self.merge_dataframes(sales_dataframes, sales_group_key)

            # 添加时间列
            sales_df = self.add_time_column(sales_df)

            # 确保两个数据框都有必要的列
            if '店铺' not in merged_df.columns or '店铺' not in sales_df.columns:
                self.logger.warning("缺少店铺列，无法进行数据合并")
                return merged_df

            # 找到客服列名（可能有不同的名称）
            work_customer_col = None
            sales_customer_col = None

            for col in merged_df.columns:
                if '客服' in col:
                    work_customer_col = col
                    break

            for col in sales_df.columns:
                if '客服' in col:
                    sales_customer_col = col
                    break

            if not work_customer_col or not sales_customer_col:
                self.logger.warning("缺少客服列，无法进行数据合并")
                return merged_df

            # 检查售前数据是否有需要的列
            required_cols = ['售前接待人数', '促成出库人数']
            missing_cols = [col for col in required_cols if col not in sales_df.columns]
            if missing_cols:
                self.logger.warning(f"售前数据缺少列: {missing_cols}")
                return merged_df

            # 准备合并的列
            merge_cols = ['时间', '店铺', sales_customer_col, '售前接待人数', '促成出库人数']
            sales_merge_df = sales_df[merge_cols].copy()

            # 重命名客服列以便合并
            sales_merge_df = sales_merge_df.rename(columns={sales_customer_col: work_customer_col})

            # 执行左连接合并
            result_df = merged_df.merge(
                sales_merge_df[['时间', '店铺', work_customer_col, '售前接待人数', '促成出库人数']],
                on=['时间', '店铺', work_customer_col],
                how='left'
            )

            # 找到解决率列的位置，在其后插入新列
            if '解决率' in result_df.columns:
                solve_rate_idx = result_df.columns.get_loc('解决率')

                # 重新排列列顺序，将新列插入到解决率后面
                cols = list(result_df.columns)
                new_cols = [col for col in cols if col not in ['售前接待人数', '促成出库人数']]

                # 在解决率后插入新列
                insert_idx = solve_rate_idx + 1
                new_cols.insert(insert_idx, '售前接待人数')
                new_cols.insert(insert_idx + 1, '促成出库人数')

                result_df = result_df[new_cols]

            self.logger.info(f"成功为京东工作量对比数据添加售前接待人数和促成出库人数列")
            return result_df

        except Exception as e:
            self.logger.error(f"处理京东数据合并时发生错误: {str(e)}")
            return merged_df

    def process_group(self, group_key, file_list):
        """
        处理一个分组的文件合并

        Args:
            group_key (str): 分组键
            file_list (list): 文件信息列表

        Returns:
            tuple: (group_key, success, message)
        """
        try:
            self.logger.info(f"处理分组: {group_key}")

            dataframes = []
            platform = file_list[0]['platform']

            for file_info in file_list:
                df = self.read_excel_file(file_info['full_path'])
                if not df.empty:
                    # 应用平台特定的数据清理
                    df_cleaned = self.clean_platform_data(df, platform)
                    if not df_cleaned.empty:
                        dataframes.append(df_cleaned)

            if not dataframes:
                warning_msg = f"分组 {group_key} 没有有效数据"
                self.logger.warning(warning_msg)
                return (group_key, False, warning_msg)

            # 合并数据
            merged_df = self.merge_dataframes(dataframes, group_key)

            if merged_df.empty:
                warning_msg = f"分组 {group_key} 合并后为空"
                self.logger.warning(warning_msg)
                return (group_key, False, warning_msg)

            # 添加时间列到第一列
            merged_df = self.add_time_column(merged_df, file_list)

            # 处理京东特殊数据合并需求
            merged_df = self.merge_jingdong_data(merged_df, group_key)

            # 生成输出文件名（不包含"_合并数据"后缀）
            data_type = file_list[0]['type']
            output_filename = f"{self.platform_names[platform]}_{data_type}.xlsx"

            # 确保输出目录存在
            output_dir = self.root_path / "合并结果"
            output_dir.mkdir(exist_ok=True)

            output_path = output_dir / output_filename

            # 保存合并结果，设置百分比格式
            self.save_excel_with_formatting(merged_df, output_path)
            success_msg = f"保存合并结果: {output_path} (包含 {len(merged_df)} 行数据)"
            self.logger.info(success_msg)
            return (group_key, True, success_msg)

        except Exception as e:
            error_msg = f"处理分组 {group_key} 时发生错误: {str(e)}"
            self.logger.error(error_msg)
            return (group_key, False, error_msg)
    
    def run(self, max_workers=4):
        """
        运行合并流程

        Args:
            max_workers (int): 最大线程数，默认为4
        """
        self.logger.info("=" * 50)
        self.logger.info("数据文件合并系统启动")
        self.logger.info("=" * 50)

        # 扫描文件
        self.scan_files()

        if not self.file_info:
            self.logger.warning("没有找到任何Excel文件")
            return

        # 使用多线程处理分组
        self.logger.info(f"开始多线程处理，使用 {max_workers} 个线程")

        success_count = 0
        failed_count = 0

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_group = {
                executor.submit(self.process_group, group_key, file_list): group_key
                for group_key, file_list in self.file_info.items()
            }

            # 收集结果
            for future in as_completed(future_to_group):
                group_key = future_to_group[future]
                try:
                    _, success, _ = future.result()
                    if success:
                        success_count += 1
                    else:
                        failed_count += 1
                except Exception as e:
                    self.logger.error(f"线程处理分组 {group_key} 时发生异常: {str(e)}")
                    failed_count += 1

        self.logger.info("=" * 50)
        self.logger.info("数据合并完成！")
        self.logger.info(f"成功处理: {success_count} 个分组")
        self.logger.info(f"失败处理: {failed_count} 个分组")
        self.logger.info("=" * 50)

if __name__ == "__main__":
    try:
        # 创建合并器实例并运行
        print("数据文件合并系统")
        print("=" * 50)
        merger = DataMerger()
        # 使用4个线程并行处理，可根据机器性能调整
        merger.run(max_workers=4)

    except ValueError as e:
        print(f"错误: {e}")
        print("程序已退出")
    except Exception as e:
        print(f"程序运行时发生错误: {e}")
        print("程序已退出")
        input("按任意键退出...")
