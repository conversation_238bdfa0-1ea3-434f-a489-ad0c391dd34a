# 数据汇总系统使用说明

## 概述
数据汇总系统是一个完整的数据处理工具，具有图形化用户界面，能够自动合并各平台的数据文件，并将数据汇总到指定的Excel模板中。系统采用单文件架构，生产级代码质量，支持多线程处理。

## 主要功能
1. **阶段0 - 数据合并**：自动扫描并合并各平台（抖音、天猫、京东、拼多多）的Excel数据文件
2. **阶段1 - 客服指标汇总**：将合并后的数据按照字段映射规则汇总到客服指标汇总表（追加模式）
3. **阶段2 - 业绩预算处理**：将数据处理后写入客服业绩预算表的各个工作表（替换模式）

## 系统特点
- **单文件架构**：整个系统实现为一个独立的Python文件
- **生产级代码**：代码整洁、无调试残留、完整异常处理
- **图形化界面**：使用tkinter创建美观的用户界面
- **多线程处理**：避免界面冻结，提高处理效率
- **智能错误处理**：能够处理各种Excel文件格式问题

## 文件结构
```
data_summary_system.py  # 主程序文件（生产级）
README.md              # 使用说明
```

## 使用方法

### 1. 运行程序
```bash
python data_summary_system.py
```

### 2. 界面操作
1. **选择源数据文件夹**：选择包含各平台数据的根目录
2. **选择客服指标汇总表**：选择要更新的客服指标汇总Excel文件（可选）
3. **选择客服业绩预算表**：选择要更新的客服业绩预算Excel文件（可选）
4. **点击"开始处理"**：系统将自动执行所有处理步骤

### 3. 数据文件要求

#### 源数据文件夹结构
```
根目录/
├── DY/          # 抖音数据文件夹
├── TM/          # 天猫数据文件夹  
├── JD/          # 京东数据文件夹
└── PDD/         # 拼多多数据文件夹
```

#### 文件命名规则
文件名格式：`店铺_类型_时间.xlsx`
- 例如：`抖音_数据_客服数据_2025-01-01.xlsx`

#### 合并后的文件格式
系统会自动将源数据合并为以下格式的文件（使用中文平台名称）：
- **抖音**：`抖音_数据_客服数据.xlsx`
- **拼多多**：`拼多多_多多客服-客服数据-客服绩效数据-客服绩效详情.xlsx`
- **天猫**：`天猫_一目了然_新客服绩效数据.xlsx`、`天猫_绩效明细_旺旺销售明细.xlsx`、`天猫_绩效明细_旺旺退款明细.xlsx`
- **京东**：`京东_客服数据对比_工作量对比.xlsx`、`京东_叮咚查询_促成订单查询.xlsx`

## 处理流程

### 阶段0：数据合并
- 扫描各平台文件夹中的Excel文件
- 按照平台和数据类型进行分组
- 合并同类型文件，处理标题不一致问题
- 添加时间列（从目录名提取年月信息）
- 清洗数据（去除前后空格，转换数字类型）
- 去除各平台的汇总行
- **文件命名**：使用中文平台名称（如：抖音_数据_客服数据.xlsx）

### 阶段1：客服指标汇总表处理
- 读取合并后的数据文件
- 按照字段映射规则转换字段名
- 清洗零值数据
- 追加数据到各平台工作表
- 汇总所有数据到"指标汇总明细"工作表

### 阶段2：客服业绩预算表处理
- 处理拼多多和抖音数据（直接写入对应工作表）
- 处理京东数据（按旗舰店/自营店分类，筛选已出库/已完成订单）
- 处理天猫数据（分别处理销售明细和退款明细）

## 数据清洗规则

### 通用清洗
- 去除字段前后空格
- 转换百分比数据为数字格式
- 转换数字字段为正确的数字类型
- 处理空值和异常值

### 平台特殊处理
- **拼多多**：转换响应时长格式（分钟秒 → 秒），去除"店铺总计"和"主账号"行
- **京东**：去除"总值"和"均值"行，合并工作量和销售数据
- **抖音**：去除"汇总"和"均值"行
- **天猫**：去除"汇总"和"均值"行

## 字段映射

### 客服指标汇总表字段映射
| 目标字段 | 拼多多 | 抖音 | 天猫 | 京东 |
|---------|--------|------|------|------|
| 年月 | 时间 | 时间 | 时间 | 时间 |
| 店铺 | 店铺 | 店铺 | 店铺 | 店铺 |
| 客服昵称 | 客服账号 | 客服昵称 | 旺旺 | 客服 |
| 平均响应时长 | 平均人工响应时长2 | 新平均响应时长（秒） | 平均响应(秒) | 平均响应时间 |
| 满意率 | - | 满意率 | 客户满意率 | 满意率 |
| 询单人数 | 询单人数 | 询单人数 | 询单人数 | 售前接待人数 |
| 销售人数 | 最终成团人数 | 支付人数 | 询单->最终付款人数 | 促成出库人数 |

## 注意事项

1. **文件权限**：确保目标Excel文件没有被其他程序打开
2. **数据备份**：处理前建议备份原始数据文件
3. **文件格式**：仅支持.xlsx格式的Excel文件
4. **内存使用**：处理大量数据时可能需要较多内存
5. **错误处理**：程序会记录详细的处理日志，出现错误时请查看日志信息

## 故障排除

### 常见问题
1. **文件读取失败**：检查文件是否存在，格式是否正确
2. **权限错误**：确保文件没有被其他程序占用
3. **内存不足**：关闭其他程序释放内存
4. **数据类型错误**：程序会自动处理大部分数据类型转换问题

### 日志查看
程序运行时会在界面下方显示详细的处理日志，包括：
- 文件扫描结果
- 数据合并进度
- 错误和警告信息
- 处理完成状态

## 功能验证报告

### 已完成的功能验证
✅ **阶段0 - 数据合并功能**
- 文件命名规则正确（使用中文平台名称：抖音_、天猫_、京东_、拼多多_）
- 文件保存在源文件夹内
- 时间列自动添加（从目录名提取年月）
- 数据清洗和类型转换正常
- 平台特殊处理规则生效

✅ **阶段1 - 客服指标汇总表处理**
- 数据追加模式正常工作（修复了空行间隙问题）
- 字段映射规则正确执行
- 零值清洗仅在指标汇总明细表应用
- 各平台数据成功汇总到明细表
- **修复**：解决了京东店铺数据追加时产生空行间隙的问题

✅ **阶段2 - 客服业绩预算表处理**
- 替换模式正常工作（清空后重新填入）
- 京东店铺分类正确（旗舰店/自营店）
- 订单状态筛选有效（仅处理已出库/已完成）
- 天猫销售和退款明细分别处理
- **修复**：移除了safe_value_convert，使用正常的数据写入逻辑

✅ **GUI界面和用户体验**
- 文件选择器功能正常
- 进度显示和日志输出完整
- 多线程处理避免界面冻结
- 错误处理和用户提示友好

✅ **Excel文件兼容性**
- 解决了"expected <class 'float'>"错误（通过更新openpyxl版本）
- 支持多种Excel文件格式
- 使用正常的数据写入逻辑（移除了复杂的safe_value_convert）
- 完整的异常恢复机制

### 测试数据统计
基于6月测试数据的验证结果：
- 源文件扫描：81个文件，12个合并组
- 数据合并：生成12个合并文件
- 客服指标汇总：处理279行汇总数据
- 业绩预算处理：
  - 拼多多：76行数据
  - 抖音：59行数据
  - 京东旗舰店：248行数据
  - 京东自营店：4816行数据
  - 天猫销售明细：8779行数据
  - 天猫退款明细：4264行数据

## 技术支持
如遇到问题，请查看程序日志中的错误信息，或联系技术支持。

## 版本信息
- 版本：v1.0 生产级
- 最后更新：2025年1月
- 状态：已完成所有功能验证，可用于生产环境
